<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>aqita_cloud</name>
  <version>0.0.1</version>
  <description>AWS Cloud Connector package</description>
  <maintainer email="<EMAIL>">todor</maintainer>
  <license>PROPRIETARY</license>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <!-- my packages  -->
  <!-- <exec_depend>python3-orjson</exec_depend>
  <exec_depend>python3-awsiotsdk</exec_depend>
  <exec_depend>python3-fastapi</exec_depend>
  <exec_depend>python3-<PERSON>ja2</exec_depend>
  <exec_depend>python3-requests</exec_depend>
  <exec_depend>python3-sse-starlette</exec_depend>
  <exec_depend>python3-uvloop</exec_depend>
  <exec_depend>python3-uvicorn</exec_depend>
  <exec_depend>python3-websockets</exec_depend>
  <exec_depend>python3-h11</exec_depend>
  <exec_depend>python3-ament-index-python</exec_depend> -->

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
