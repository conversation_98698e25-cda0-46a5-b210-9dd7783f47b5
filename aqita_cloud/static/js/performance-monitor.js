/**
 * Performance Monitor Class
 * Tracks FPS, memory usage, and data points for performance optimization
 */
class PerformanceMonitor {
    constructor() {
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
        this.isVisible = false;
        
        this.fpsElement = document.getElementById('fpsCounter');
        this.memoryElement = document.getElementById('memoryUsage');
        this.dataPointsElement = document.getElementById('dataPointsCount');
        this.monitorElement = document.getElementById('performanceMonitor');
        
        // Start monitoring
        this.startMonitoring();
        
        // Toggle button
        document.getElementById('togglePerformanceMonitor').addEventListener('click', () => {
            this.toggle();
        });
    }
    
    startMonitoring() {
        const updateStats = () => {
            this.frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - this.lastTime >= 1000) {
                this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
                this.frameCount = 0;
                this.lastTime = currentTime;
                
                if (this.isVisible) {
                    this.updateDisplay();
                }
            }
            
            requestAnimationFrame(updateStats);
        };
        
        requestAnimationFrame(updateStats);
    }
    
    updateDisplay() {
        this.fpsElement.textContent = this.fps;
        
        // Memory usage (if available)
        if (performance.memory) {
            const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            this.memoryElement.textContent = memoryMB;
        } else {
            this.memoryElement.textContent = 'N/A';
        }
        
        // Data points count will be updated by DataManager
    }
    
    updateDataPointsCount(count) {
        if (this.isVisible) {
            this.dataPointsElement.textContent = count;
        }
    }
    
    toggle() {
        this.isVisible = !this.isVisible;
        this.monitorElement.style.display = this.isVisible ? 'block' : 'none';
        if (this.isVisible) {
            this.updateDisplay();
        }
    }
}

// Export for use in other modules
window.PerformanceMonitor = PerformanceMonitor;
