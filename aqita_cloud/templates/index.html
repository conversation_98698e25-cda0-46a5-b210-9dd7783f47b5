{% extends "base.html" %}

{% block title %}Home - Real-time Metrics{% endblock %}

{% block content %}
<div class="container-fluid px-2 h-100">
    <!-- Performance Monitor -->
    <div id="performanceMonitor" class="position-fixed top-0 end-0 m-2 p-2 bg-dark text-white rounded" style="z-index: 1050; font-size: 0.8rem; display: none;">
        <div>FPS: <span id="fpsCounter">0</span></div>
        <div>Memory: <span id="memoryUsage">0</span>MB</div>
        <div>Points: <span id="dataPointsCount">0</span></div>
    </div>

    <div class="row h-100 g-2">
        <div class="col-md-9">
            <div class="card h-100">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex flex-wrap justify-content-end align-items-center gap-2 mb-2">
                        <div id="cursorTimestamp" class="text-muted" style="display: none; min-width: 120px; font-size: 0.9rem;"></div>
                        <div id="selectionTimeRange" class="text-muted" style="display: none; font-size: 0.8rem; white-space: nowrap;">
                            <strong>Selection:</strong>
                            <span id="selectionStartTime" class="ms-1"></span>
                            <span class="mx-1">→</span>
                            <span id="selectionEndTime"></span>
                            <span class="mx-1">|</span>
                            <span id="selectionDuration" class="text-primary"></span>
                        </div>
                        <div class="d-flex align-items-center gap-2" style="min-width: 200px; max-width: 300px;">
                            <label for="timeScroll" class="mb-0">Scroll:</label>
                            <input type="range" class="form-range flex-grow-1" id="timeScroll" min="0" max="100" value="50">
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success" id="followButton">
                                <i class="bi bi-play-fill"></i> Follow
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="pauseButton">
                                <i class="bi bi-pause-fill"></i> Pause
                            </button>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" data-period="30">30s</button>
                            <button type="button" class="btn btn-outline-primary" data-period="60">1m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="180">3m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="300">5m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="600">10m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="900">15m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="1800">30m</button>
                        </div>
                        <button type="button" class="btn btn-outline-info btn-sm" id="togglePerformanceMonitor">
                            <i class="bi bi-speedometer2"></i>
                        </button>
                    </div>
                    <div class="flex-grow-1 position-relative">
                        <canvas id="metricChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3" id="metricsPanel">
            <div class="d-flex flex-column h-100 gap-2">
                <div class="card flex-grow-1 d-flex flex-column">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Available Metrics</h5>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" id="enableAllMetrics">
                                    <i class="bi bi-check-all"></i> Enable All
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="disableAllMetrics">
                                    <i class="bi bi-x-lg"></i> Disable All
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body overflow-auto flex-grow-1">
                        <div class="list-group" id="metricList">
                            <!-- Metrics will be added here dynamically -->
                        </div>
                    </div>
                </div>
                <div class="card flex-grow-1 d-flex flex-column">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Metric Selection</h5>
                    </div>
                    <div class="card-body overflow-auto flex-grow-1">
                        <div id="selectionStats">
                            <p class="text-muted">Select a region on the chart to view statistics</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Streaming Panel -->
    <div class="row mt-2">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">System Logs</h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">Clear</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="scrollToBottom()">Scroll to Bottom</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="scrollToTop()">Jump to Top</button>
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#logPanel" aria-expanded="false">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="collapse" id="logPanel">
                    <div class="card-body p-0">
                        <div class="table-responsive" style="max-height: 80vh; overflow-y: auto;">
                            <table class="table table-sm log-table mb-0">
                                <thead class="sticky-top bg-body">
                                    <tr>
                                        <th style="width: 15%">Timestamp</th>
                                        <th style="width: 10%">Level</th>
                                        <th style="width: 15%">Thread</th>
                                        <th style="width: 60%">Message</th>
                                    </tr>
                                </thead>
                                <tbody id="logTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="{{ url_for('static', path='css/metrics.css') }}">
<link rel="stylesheet" href="{{ url_for('static', path='css/bootstrap-icons.css') }}">
<script src="{{ url_for('static', path='js/chart.umd.js') }}"></script>
<script src="{{ url_for('static', path='js/chartjs-adapter-date-fns.bundle.min.js') }}"></script>
<script src="{{ url_for('static', path='js/performance-monitor.js') }}"></script>
<script src="{{ url_for('static', path='js/color-manager.js') }}"></script>
<script src="{{ url_for('static', path='js/data-manager.js') }}"></script>
<script src="{{ url_for('static', path='js/chart-manager.js') }}"></script>

<script>
// Main application script - uses classes from separate JS files

// Classes are now loaded from separate JS files

// ChartManager is now loaded from separate JS file

// SSE Manager Class
class SSEManager {
    constructor(dataManager, onMetricReceived) {
        this.dataManager = dataManager;
        this.onMetricReceived = onMetricReceived;
        this.eventSource = null;
        this.batchBuffer = [];
        this.batchSize = 10;
        this.batchTimeout = 50; // ms
        this.batchTimer = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.lastDataReceived = null; // Track when data was last received

        this.connect();
    }

    connect() {
        try {
            this.eventSource = new EventSource('/events');

            this.eventSource.addEventListener('metric', (e) => {
                const event = JSON.parse(e.data);
                this.addToBatch(event);
            });

            this.eventSource.addEventListener('metric_batch', (e) => {
                const events = JSON.parse(e.data);
                events.forEach(event => this.addToBatch(event));
            });

            this.eventSource.addEventListener('heartbeat', (e) => {
                // Keep connection alive
                const data = JSON.parse(e.data || '{}');
                if (data.timestamp) {
                    console.log('Heartbeat received at', new Date(data.timestamp * 1000));
                }
            });

            this.eventSource.onerror = (error) => {
                console.error('SSE Error:', error);
                this.handleReconnect();
            };

            this.eventSource.onopen = () => {
                console.log('SSE Connected');
                this.reconnectAttempts = 0;
            };

        } catch (error) {
            console.error('Failed to create SSE connection:', error);
            this.handleReconnect();
        }
    }

    addToBatch(event) {
        this.batchBuffer.push(event);
        this.lastDataReceived = Date.now(); // Track when data was received

        if (this.batchBuffer.length >= this.batchSize) {
            this.processBatch();
        } else if (!this.batchTimer) {
            this.batchTimer = setTimeout(() => {
                this.processBatch();
            }, this.batchTimeout);
        }
    }

    processBatch() {
        if (this.batchBuffer.length === 0) return;

        const batch = this.batchBuffer.splice(0);

        batch.forEach(event => {
            try {
                const timestamp = new Date(event.timestamp);
                if (!isNaN(timestamp.getTime()) && typeof event.value === 'number') {
                    this.dataManager.addDataPoint(event.name, timestamp, event.value);
                }
            } catch (error) {
                console.warn('Invalid SSE data point:', event, error);
            }
        });

        if (this.onMetricReceived) {
            this.onMetricReceived(batch);
        }

        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
    }

    handleReconnect() {
        if (this.eventSource) {
            this.eventSource.close();
        }

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

            console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }

    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
        }
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
        }
    }
}

// Metrics Panel Manager Class
class MetricsPanelManager {
    constructor(dataManager, colorManager, onMetricToggle) {
        this.dataManager = dataManager;
        this.colorManager = colorManager;
        this.onMetricToggle = onMetricToggle;
        this.metricListElement = document.getElementById('metricList');
        this.needsUpdate = false;

        // Throttled update function
        this.throttledUpdate = this.throttle(() => {
            if (this.needsUpdate) {
                this.updateMetricList();
                this.needsUpdate = false;
            }
        }, 200);

        this.setupEventListeners();
    }

    setupEventListeners() {
        document.getElementById('enableAllMetrics').addEventListener('click', () => {
            this.dataManager.getAllMetrics().forEach(metric => {
                this.dataManager.setMetricActive(metric, true);
            });
            this.requestUpdate();
            if (this.onMetricToggle) this.onMetricToggle();
        });

        document.getElementById('disableAllMetrics').addEventListener('click', () => {
            this.dataManager.getAllMetrics().forEach(metric => {
                this.dataManager.setMetricActive(metric, false);
            });
            this.requestUpdate();
            if (this.onMetricToggle) this.onMetricToggle();
        });
    }

    updateMetricList() {
        const metrics = this.dataManager.getAllMetrics().sort();
        const activeMetrics = this.dataManager.getActiveMetrics();

        // Use DocumentFragment for efficient DOM updates
        const fragment = document.createDocumentFragment();

        metrics.forEach(metric => {
            const color = this.colorManager.getMetricColor(metric);
            const isActive = activeMetrics.includes(metric);

            const item = this.createMetricItem(metric, color, isActive);
            fragment.appendChild(item);
        });

        // Replace all children at once
        this.metricListElement.innerHTML = '';
        this.metricListElement.appendChild(fragment);
    }

    createMetricItem(metric, color, isActive) {
        const item = document.createElement('a');
        item.href = '#';
        item.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';
        item.style.borderLeft = `4px solid ${color}`;

        item.innerHTML = `
            <span class="${isActive ? 'text-primary' : ''}">${metric}</span>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" role="switch"
                    id="toggle-${metric}" ${isActive ? 'checked' : ''}>
            </div>
        `;

        const toggle = item.querySelector(`#toggle-${metric}`);
        toggle.addEventListener('change', (e) => {
            e.stopPropagation();
            const metricName = item.querySelector('span');
            const isChecked = toggle.checked;

            this.dataManager.setMetricActive(metric, isChecked);

            if (isChecked) {
                metricName.classList.add('text-primary');
            } else {
                metricName.classList.remove('text-primary');
            }

            if (this.onMetricToggle) this.onMetricToggle();
        });

        return item;
    }

    requestUpdate() {
        this.needsUpdate = true;
        this.throttledUpdate();
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }
}

// Main Application Controller
class MetricsApp {
    constructor() {
        this.maxDataPoints = 30;
        this.timeWindowState = {
            start: null,
            end: null,
            isLive: true,
            scrollPosition: 50
        };

        // Track time window changes for pause functionality
        this.lastTimeWindowStart = null;
        this.lastTimeWindowEnd = null;
        this.lastMaxDataPoints = this.maxDataPoints;

        // Initialize managers
        this.performanceMonitor = new PerformanceMonitor();
        this.colorManager = new ColorManager();
        this.dataManager = new DataManager(500000); // Much larger buffer to keep all data
        this.chartManager = new ChartManager('metricChart', this.dataManager, this.colorManager);
        this.metricsPanelManager = new MetricsPanelManager(
            this.dataManager,
            this.colorManager,
            () => this.onMetricToggle()
        );

        // Initialize SSE with batching
        this.sseManager = new SSEManager(
            this.dataManager,
            (batch) => this.onMetricsReceived(batch)
        );

        this.setupEventListeners();
        this.updateButtonStates(); // Initialize button states
        this.startUpdateLoop();
        this.loadHistoricalData();
    }

    setupEventListeners() {
        // Follow/Pause buttons
        document.getElementById('followButton').addEventListener('click', () => {
            this.timeWindowState.isLive = true;
            this.timeWindowState.scrollPosition = 100;
            document.getElementById('timeScroll').value = 100;
            this.clearSelection();
            this.updateButtonStates();
            // Restart animation when going live
            this.startAnimation();
        });

        document.getElementById('pauseButton').addEventListener('click', () => {
            if (this.timeWindowState.isLive) {
                // Capture current time window when pausing
                const now = new Date();
                const timeWindow = this.maxDataPoints * 1000;
                this.timeWindowState.end = now;
                this.timeWindowState.start = new Date(now.getTime() - timeWindow);

                // Calculate scroll position based on current view
                const bounds = this.dataManager.getTimeBounds();
                if (bounds.min && bounds.max) {
                    const totalTimeRange = bounds.max.getTime() - bounds.min.getTime();

                    if (totalTimeRange > timeWindow) {
                        // Normal case: data range is larger than time window
                        const maxScrollOffset = totalTimeRange - timeWindow;
                        const currentOffset = this.timeWindowState.start.getTime() - bounds.min.getTime();
                        this.timeWindowState.scrollPosition = Math.min(100, Math.max(0, (currentOffset / maxScrollOffset) * 100));
                    } else {
                        // Chart is full: time window covers all or more than all data
                        this.timeWindowState.scrollPosition = 50; // Center position
                        this.timeWindowState.start = bounds.min;
                        this.timeWindowState.end = bounds.max;
                    }

                    document.getElementById('timeScroll').value = this.timeWindowState.scrollPosition;
                }
            }

            this.timeWindowState.isLive = false;
            this.updateButtonStates();
            // Animation will stop automatically when paused and no recent updates
        });

        // Time period buttons
        document.querySelectorAll('.btn-group .btn[data-period]').forEach(button => {
            button.addEventListener('click', (e) => {
                document.querySelectorAll('.btn-group .btn[data-period]').forEach(btn =>
                    btn.classList.remove('active')
                );
                e.target.classList.add('active');

                this.maxDataPoints = parseInt(e.target.dataset.period);
                this.chartManager.setMaxDataPoints(this.maxDataPoints);

                // Update time window even when paused
                if (this.timeWindowState.isLive) {
                    // In live mode, keep following
                    this.timeWindowState.scrollPosition = 100;
                    document.getElementById('timeScroll').value = 100;
                } else {
                    // In paused mode, recalculate time window with new span
                    this.updateTimeWindow();
                }

                this.clearSelection();
                this.updateButtonStates();
                // Briefly restart animation to update the chart when changing time period
                this.startAnimation();
            });
        });

        // Time scroll - should pause the chart
        document.getElementById('timeScroll').addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            this.timeWindowState.scrollPosition = value;
            this.timeWindowState.isLive = false; // Pause when scrolling
            this.clearSelection();

            // Force update of time window based on scroll position
            const bounds = this.dataManager.getTimeBounds();
            if (bounds.min && bounds.max) {
                const totalTimeRange = bounds.max.getTime() - bounds.min.getTime();
                const timeWindow = this.maxDataPoints * 1000;

                if (totalTimeRange > timeWindow) {
                    // Normal scrolling when data range is larger than time window
                    const maxScrollOffset = totalTimeRange - timeWindow;
                    const scrollOffset = maxScrollOffset * (value / 100);
                    this.timeWindowState.start = new Date(bounds.min.getTime() + scrollOffset);
                    this.timeWindowState.end = new Date(this.timeWindowState.start.getTime() + timeWindow);
                } else {
                    // When time window covers all data, center the data
                    this.timeWindowState.start = bounds.min;
                    this.timeWindowState.end = bounds.max;
                }
            }

            this.updateButtonStates();
            // Briefly restart animation to update the chart when scrolling
            this.startAnimation();
        });
    }

    updateButtonStates() {
        const followButton = document.getElementById('followButton');
        const pauseButton = document.getElementById('pauseButton');

        if (this.timeWindowState.isLive) {
            followButton.classList.add('btn-success');
            followButton.classList.remove('btn-outline-success');
            pauseButton.classList.add('btn-outline-secondary');
            pauseButton.classList.remove('btn-secondary');
        } else {
            followButton.classList.add('btn-outline-success');
            followButton.classList.remove('btn-success');
            pauseButton.classList.add('btn-secondary');
            pauseButton.classList.remove('btn-outline-secondary');
        }
    }

    onMetricsReceived(batch) {
        // Update metrics panel if new metrics were added
        const hasNewMetrics = batch.some(event =>
            !this.dataManager.receivedMetrics.has(event.name)
        );

        if (hasNewMetrics) {
            this.metricsPanelManager.requestUpdate();
        }

        // Update performance monitor
        this.performanceMonitor.updateDataPointsCount(this.dataManager.getTotalDataPoints());

        // Restart animation when new data arrives (for paused charts with recent data)
        if (!this.timeWindowState.isLive && this.hasRecentDataUpdates()) {
            this.startAnimation();
        }
    }

    onMetricToggle() {
        this.chartManager.updateDatasets();
    }

    startUpdateLoop() {
        let lastUpdateTime = 0;
        const targetFPS = 30; // Reduced from 60 for better performance with many metrics
        const frameTime = 1000 / targetFPS;
        this.animationFrameId = null;
        this.isAnimating = false;

        const update = (currentTime) => {
            if (currentTime - lastUpdateTime >= frameTime) {
                const startTime = performance.now();
                this.updateVisibleData();
                const updateDuration = performance.now() - startTime;

                // Check if we need to continue animating
                const needsContinuousUpdate = this.timeWindowState.isLive || this.hasRecentDataUpdates();

                if (needsContinuousUpdate) {
                    // Adaptive frame rate based on update time
                    if (updateDuration > frameTime * 2) {
                        // If update takes too long, reduce frequency
                        setTimeout(() => {
                            if (this.isAnimating) {
                                this.animationFrameId = requestAnimationFrame(update);
                            }
                        }, frameTime * 2);
                    } else {
                        lastUpdateTime = currentTime;
                        if (this.isAnimating) {
                            this.animationFrameId = requestAnimationFrame(update);
                        }
                    }
                } else {
                    // Chart is paused and no recent updates, stop animation loop
                    this.stopAnimation();
                }
            } else {
                if (this.isAnimating) {
                    this.animationFrameId = requestAnimationFrame(update);
                }
            }
        };

        this.startAnimation = () => {
            if (!this.isAnimating) {
                this.isAnimating = true;
                this.animationFrameId = requestAnimationFrame(update);
            }
        };

        this.stopAnimation = () => {
            this.isAnimating = false;
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
                this.animationFrameId = null;
            }
        };

        // Start the animation loop
        this.startAnimation();
    }

    hasRecentDataUpdates() {
        // Check if there have been recent data updates that require animation
        const now = Date.now();
        const recentThreshold = 2000; // 2 seconds

        // Check if SSE manager has recent activity
        if (this.sseManager && this.sseManager.lastDataReceived) {
            return (now - this.sseManager.lastDataReceived) < recentThreshold;
        }

        return false;
    }

    updateVisibleData() {
        const now = new Date();
        const timeWindow = this.maxDataPoints * 1000;

        if (this.timeWindowState.isLive) {
            this.timeWindowState.end = now;
            this.timeWindowState.start = new Date(now.getTime() - timeWindow);
            this.timeWindowState.scrollPosition = 100;
            document.getElementById('timeScroll').value = 100;

            this.chartManager.updateTimeWindow(this.timeWindowState.start, this.timeWindowState.end);
            this.chartManager.updateVisibleData(this.timeWindowState.start, this.timeWindowState.end);
        } else {
            // When paused, check if we need to update the time window
            const needsUpdate =
                this.lastTimeWindowStart !== this.timeWindowState.start?.getTime() ||
                this.lastTimeWindowEnd !== this.timeWindowState.end?.getTime() ||
                this.lastMaxDataPoints !== this.maxDataPoints;

            if (needsUpdate) {
                this.updateTimeWindow();
                this.chartManager.updateTimeWindow(this.timeWindowState.start, this.timeWindowState.end);
                this.chartManager.updateVisibleData(this.timeWindowState.start, this.timeWindowState.end);

                this.lastTimeWindowStart = this.timeWindowState.start?.getTime();
                this.lastTimeWindowEnd = this.timeWindowState.end?.getTime();
                this.lastMaxDataPoints = this.maxDataPoints;
            }
        }
    }

    updateTimeWindow() {
        const bounds = this.dataManager.getTimeBounds();
        if (!bounds.min || !bounds.max) return;

        const totalTimeRange = bounds.max.getTime() - bounds.min.getTime();
        const timeWindow = this.maxDataPoints * 1000;

        if (this.timeWindowState.isLive) {
            // Live mode - always show latest data
            const now = new Date();
            this.timeWindowState.end = now;
            this.timeWindowState.start = new Date(now.getTime() - timeWindow);
        } else {
            // Paused mode - maintain relative position but adjust for new timespan
            if (this.timeWindowState.start && this.timeWindowState.end) {
                // Try to keep the center of the current view
                const currentCenter = (this.timeWindowState.start.getTime() + this.timeWindowState.end.getTime()) / 2;
                this.timeWindowState.start = new Date(currentCenter - timeWindow / 2);
                this.timeWindowState.end = new Date(currentCenter + timeWindow / 2);

                // Adjust if we're outside bounds
                if (this.timeWindowState.start < bounds.min) {
                    this.timeWindowState.start = bounds.min;
                    this.timeWindowState.end = new Date(bounds.min.getTime() + timeWindow);
                }
                if (this.timeWindowState.end > bounds.max) {
                    this.timeWindowState.end = bounds.max;
                    this.timeWindowState.start = new Date(bounds.max.getTime() - timeWindow);
                }

                // Update scroll position to match new window
                const maxScrollOffset = Math.max(0, totalTimeRange - timeWindow);
                if (maxScrollOffset > 0) {
                    const currentOffset = this.timeWindowState.start.getTime() - bounds.min.getTime();
                    this.timeWindowState.scrollPosition = Math.min(100, Math.max(0, (currentOffset / maxScrollOffset) * 100));
                    document.getElementById('timeScroll').value = this.timeWindowState.scrollPosition;
                } else {
                    // When time window is larger than or equal to total data range
                    this.timeWindowState.scrollPosition = 0;
                    document.getElementById('timeScroll').value = 0;
                }
            } else {
                // Fallback to scroll-based positioning
                if (totalTimeRange > timeWindow) {
                    // Normal scrolling when data range is larger than time window
                    const maxScrollOffset = totalTimeRange - timeWindow;
                    const scrollOffset = maxScrollOffset * (this.timeWindowState.scrollPosition / 100);

                    this.timeWindowState.start = new Date(bounds.min.getTime() + scrollOffset);
                    this.timeWindowState.end = new Date(this.timeWindowState.start.getTime() + timeWindow);
                } else {
                    // When time window covers all data, show all data
                    this.timeWindowState.start = bounds.min;
                    this.timeWindowState.end = bounds.max;
                    this.timeWindowState.scrollPosition = 50; // Center position
                    document.getElementById('timeScroll').value = 50;
                }
            }
        }
    }

    async loadHistoricalData() {
        try {
            let offset = 0;
            const limit = 5000; // Load in chunks of 5000
            let hasMore = true;
            let totalLoaded = 0;

            console.log('Loading historical data...');

            while (hasMore) {
                const response = await fetch(`/historical_data?limit=${limit}&offset=${offset}`);
                const result = await response.json();

                if (result.data && result.data.length > 0) {
                    // Process in smaller chunks to avoid blocking the UI
                    const chunkSize = 500;
                    for (let i = 0; i < result.data.length; i += chunkSize) {
                        const chunk = result.data.slice(i, i + chunkSize);

                        chunk.forEach(event => {
                            try {
                                const timestamp = new Date(event.timestamp);
                                if (!isNaN(timestamp.getTime()) && typeof event.value === 'number') {
                                    this.dataManager.addDataPoint(event.name, timestamp, event.value);
                                }
                            } catch (error) {
                                console.warn('Invalid data point:', event, error);
                            }
                        });

                        // Yield control to the browser every chunk
                        await new Promise(resolve => setTimeout(resolve, 0));
                    }

                    totalLoaded += result.data.length;
                    offset += result.data.length;
                    hasMore = result.pagination.has_more;

                    console.log(`Loaded ${totalLoaded} / ${result.pagination.total} historical data points`);

                    // Update UI periodically
                    if (totalLoaded % 10000 === 0) {
                        this.metricsPanelManager.requestUpdate();
                        this.chartManager.updateDatasets();
                    }

                } else {
                    hasMore = false;
                }

                // Add a small delay between requests to avoid overwhelming the server
                if (hasMore) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            }

            console.log(`Finished loading ${totalLoaded} historical data points`);
            this.metricsPanelManager.requestUpdate();
            this.chartManager.updateDatasets();

        } catch (error) {
            console.error('Error loading historical data:', error);
        }
    }

    clearSelection() {
        const selectionBox = document.querySelector('.chart-selection');
        if (selectionBox) {
            selectionBox.style.display = 'none';
        }

        // Hide the selection time range display
        const selectionTimeRange = document.getElementById('selectionTimeRange');
        if (selectionTimeRange) {
            selectionTimeRange.style.display = 'none';
        }

        document.getElementById('selectionStats').innerHTML =
            '<p class="text-muted">Select a region on the chart to view statistics</p>';
    }
}

// Selection and Statistics Manager
class SelectionManager {
    constructor(chartManager, dataManager) {
        this.chartManager = chartManager;
        this.dataManager = dataManager;
        this.isSelecting = false;
        this.selectionStart = null;
        this.selectionEnd = null;

        this.setupSelectionElements();
        this.setupEventListeners();
    }

    setupSelectionElements() {
        const chartContainer = document.querySelector('.position-relative');

        // Selection box
        this.selectionBox = document.createElement('div');
        this.selectionBox.className = 'chart-selection';
        this.selectionBox.style.display = 'none';
        chartContainer.appendChild(this.selectionBox);

        // Cursor line
        this.cursorLine = document.createElement('div');
        this.cursorLine.className = 'chart-cursor';
        chartContainer.appendChild(this.cursorLine);
    }

    setupEventListeners() {
        const chartContainer = document.querySelector('.position-relative');

        chartContainer.addEventListener('mousedown', (e) => this.onMouseDown(e));
        chartContainer.addEventListener('mousemove', (e) => this.onMouseMove(e));
        chartContainer.addEventListener('mouseup', (e) => this.onMouseUp(e));
        chartContainer.addEventListener('mouseleave', () => this.onMouseLeave());
    }

    onMouseDown(e) {
        if (!timeWindowState.isLive) {
            this.isSelecting = true;
            const rect = e.currentTarget.getBoundingClientRect();
            this.selectionStart = e.clientX - rect.left;
            this.selectionBox.style.left = this.selectionStart + 'px';
            this.selectionBox.style.top = '0';
            this.selectionBox.style.height = rect.height + 'px';
            this.selectionBox.style.width = '0';
            this.selectionBox.style.display = 'block';
        }
    }

    onMouseMove(e) {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;

        // Update cursor line
        this.cursorLine.style.left = x + 'px';
        this.cursorLine.style.top = '0';
        this.cursorLine.style.height = rect.height + 'px';
        this.cursorLine.style.display = 'block';

        // Update timestamp display - always update regardless of live/pause state
        const chart = this.chartManager.getChart();
        if (chart && chart.scales && chart.scales.x) {
            const timestamp = chart.scales.x.getValueForPixel(x);
            if (timestamp && !isNaN(timestamp)) {
                this.updateTimestampDisplay(new Date(timestamp));
            }
        }

        // Update selection box
        if (this.isSelecting) {
            const width = x - this.selectionStart;
            this.selectionBox.style.width = Math.abs(width) + 'px';
            this.selectionBox.style.left = (width < 0 ? x : this.selectionStart) + 'px';
        }
    }

    onMouseUp(e) {
        if (this.isSelecting) {
            this.isSelecting = false;
            const rect = e.currentTarget.getBoundingClientRect();
            this.selectionEnd = e.clientX - rect.left;

            const startX = Math.min(this.selectionStart, this.selectionEnd);
            const endX = Math.max(this.selectionStart, this.selectionEnd);

            const chart = this.chartManager.getChart();
            const startTime = chart.scales.x.getValueForPixel(startX);
            const endTime = chart.scales.x.getValueForPixel(endX);

            this.updateSelectionStats(new Date(startTime), new Date(endTime));
        }
    }

    onMouseLeave() {
        this.cursorLine.style.display = 'none';
        document.getElementById('cursorTimestamp').style.display = 'none';
        if (this.isSelecting) {
            this.isSelecting = false;
            this.selectionBox.style.display = 'none';
        }
    }

    updateTimestampDisplay(date) {
        const timestampDisplay = document.getElementById('cursorTimestamp');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
        timestampDisplay.textContent = `${hours}:${minutes}:${seconds}.${milliseconds}`;
        timestampDisplay.style.display = 'block';
    }

    updateSelectionStats(startTime, endTime) {
        const stats = this.calculateStats(startTime, endTime);
        const statsContainer = document.getElementById('selectionStats');

        // Format the time range
        const formatTime = (date) => {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            const ms = date.getMilliseconds().toString().padStart(3, '0');
            return `${hours}:${minutes}:${seconds}.${ms}`;
        };

        const duration = ((endTime.getTime() - startTime.getTime()) / 1000).toFixed(3);

        // Update the selection time range display above the chart (horizontal layout)
        const selectionTimeRange = document.getElementById('selectionTimeRange');
        const selectionStartTime = document.getElementById('selectionStartTime');
        const selectionEndTime = document.getElementById('selectionEndTime');
        const selectionDuration = document.getElementById('selectionDuration');

        selectionStartTime.textContent = formatTime(startTime);
        selectionEndTime.textContent = formatTime(endTime);
        selectionDuration.textContent = `${duration}s`;
        selectionTimeRange.style.display = 'inline-block';

        // Update the stats panel
        if (Object.keys(stats).length === 0) {
            statsContainer.innerHTML = '<p class="text-muted">No data in selected range</p>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-sm">';
        html += '<thead><tr><th>Metric</th><th>Min</th><th>Max</th><th>Avg</th><th>Median</th></tr></thead><tbody>';

        Object.entries(stats).forEach(([metric, values]) => {
            html += `<tr>
                <td>${metric}</td>
                <td>${values.min.toFixed(3)}</td>
                <td>${values.max.toFixed(3)}</td>
                <td>${values.avg.toFixed(3)}</td>
                <td>${values.median.toFixed(3)}</td>
            </tr>`;
        });

        html += '</tbody></table></div>';
        statsContainer.innerHTML = html;
    }

    calculateStats(startTime, endTime) {
        const stats = {};
        const activeMetrics = this.dataManager.getActiveMetrics();

        activeMetrics.forEach(metric => {
            const data = this.dataManager.getDataInRange(metric, startTime, endTime);
            const values = data.map(point => point.y);

            if (values.length > 0) {
                stats[metric] = {
                    min: Math.min(...values),
                    max: Math.max(...values),
                    avg: values.reduce((a, b) => a + b, 0) / values.length,
                    median: this.calculateMedian(values)
                };
            }
        });

        return stats;
    }

    calculateMedian(values) {
        const sorted = values.slice().sort((a, b) => a - b);
        const middle = Math.floor(sorted.length / 2);

        if (sorted.length % 2 === 0) {
            return (sorted[middle - 1] + sorted[middle]) / 2;
        }

        return sorted[middle];
    }
}

// Initialize the application
let app;
let selectionManager;

// Global timeWindowState for compatibility
let timeWindowState;

document.addEventListener('DOMContentLoaded', () => {
    app = new MetricsApp();
    timeWindowState = app.timeWindowState; // For compatibility with selection manager
    selectionManager = new SelectionManager(app.chartManager, app.dataManager);
});

// Legacy functions removed - functionality moved to class-based architecture

// Selection functionality moved to SelectionManager class

// Log streaming functionality
let logEventSource;
const logTableBody = document.getElementById('logTableBody');
const maxLogs = 1000; // Maximum number of logs to keep in memory

function addLogEntry(log) {
    const row = document.createElement('tr');
    row.className = `log-level-${log.level}`;
    
    const timestampCell = document.createElement('td');
    timestampCell.textContent = new Date(log.timestamp).toLocaleString();
    
    const levelCell = document.createElement('td');
    levelCell.textContent = log.level;
    
    const threadCell = document.createElement('td');
    threadCell.textContent = log.thread;
    threadCell.style.fontFamily = 'monospace';
    threadCell.style.fontSize = '0.9em';
    
    const messageCell = document.createElement('td');
    messageCell.className = 'log-entry';
    messageCell.textContent = log.message;
    
    row.appendChild(timestampCell);
    row.appendChild(levelCell);
    row.appendChild(threadCell);
    row.appendChild(messageCell);
    
    logTableBody.insertBefore(row, logTableBody.firstChild);
    
    // Remove oldest log if we exceed maxLogs
    if (logTableBody.children.length > maxLogs) {
        logTableBody.removeChild(logTableBody.lastChild);
    }
}

function clearLogs() {
    logTableBody.innerHTML = '';
}

function scrollToBottom() {
    const logPanel = document.getElementById('logPanel');
    if (logPanel.classList.contains('show')) {
        const tableContainer = logPanel.querySelector('.table-responsive');
        tableContainer.scrollTop = tableContainer.scrollHeight;
    }
}

function scrollToTop() {
    const logPanel = document.getElementById('logPanel');
    if (logPanel.classList.contains('show')) {
        const tableContainer = logPanel.querySelector('.table-responsive');
        tableContainer.scrollTop = 0;   
    }
}

async function loadHistoricalLogs() {
    try {
        const response = await fetch('/log_historical');
        const logs = await response.json();
        logs.forEach(log => addLogEntry(log));
    } catch (error) {
        console.error('Error loading historical logs:', error);
    }
}

function startLogEventSource() {
    if (logEventSource) {
        logEventSource.close();
    }

    logEventSource = new EventSource('/log_current');
    
    logEventSource.addEventListener('log', (event) => {
        const log = JSON.parse(event.data);
        addLogEntry(log);
    });

    logEventSource.addEventListener('error', (error) => {
        console.error('SSE Error:', error);
        // Attempt to reconnect after 5 seconds
        setTimeout(startLogEventSource, 5000);
    });
}

// Initialize log streaming
// loadHistoricalLogs();
// startLogEventSource();

// Cleanup on page unload
// window.addEventListener('beforeunload', () => {
//     if (logEventSource) {
//         logEventSource.close();
//     }
// });
</script>
{% endblock %} 